{"name": "css-to-mat", "version": "1.1.1", "description": "css-to-mat", "main": "./dist/css-to-mat.cjs.js", "module": "./dist/css-to-mat.esm.js", "sideEffects": false, "types": "declaration/index.d.ts", "scripts": {"start": "rollup -c -w", "build": "rollup -c && npm run declaration && print-sizes ./dist", "declaration": "rm -rf declaration && tsc -p tsconfig.declaration.json", "doc": "rm -rf ./doc && jsdoc -c jsdoc.json", "prerelease": "npm run build && prerelease --dirs=dist", "release": "npm run build && release --dirs=dist", "release:init": "npm run build && release -i --dirs=dist", "test": "jest --watchAll", "coverage": "jest --coverage && print-coveralls --sort=desc", "coveralls": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/daybrush/css-to-mat.git"}, "author": "Daybrush", "license": "MIT", "bugs": {"url": "https://github.com/daybrush/css-to-mat/issues"}, "homepage": "https://github.com/daybrush/css-to-mat#readme", "dependencies": {"@daybrush/utils": "^1.13.0", "@scena/matrix": "^1.0.0"}, "keywords": ["css-to-mat", "css", "transform", "mat", "mat4"], "devDependencies": {"@daybrush/builder": "^0.2.4", "@daybrush/jsdoc": "^0.4.7", "@daybrush/release": "^0.7.1", "@types/jest": "^24.0.13", "coveralls": "^3.0.3", "daybrush-jsdoc-template": "^1.10.0", "gl-matrix": "^3.3.0", "jest": "^24.8.0", "print-coveralls": "^1.2.2", "print-sizes": "0.0.4", "ts-jest": "^24.0.2", "typescript": "^4.5.0"}}