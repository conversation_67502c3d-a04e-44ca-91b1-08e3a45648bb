{"name": "css-styled", "version": "1.0.8", "description": "This component is a lightweight, simple line style component.", "main": "./dist/styled.cjs.js", "module": "./dist/styled.esm.js", "sideEffects": false, "types": "declaration/styled.d.ts", "scripts": {"start": "rollup -c -w", "build": "rollup -c && npm run declaration && print-sizes ./dist", "declaration": "rm -rf declaration && tsc -p tsconfig.declaration.json"}, "repository": {"type": "git", "url": "git+https://github.com/daybrush/css-styled.git"}, "author": "Daybrush", "license": "MIT", "bugs": {"url": "https://github.com/daybrush/css-styled/issues"}, "files": ["./*", "src/*", "dist/*", "declaration/*", "README.md"], "homepage": "https://github.com/daybrush/css-styled#readme", "dependencies": {"@daybrush/utils": "^1.13.0"}, "devDependencies": {"@daybrush/builder": "^0.1.2", "jest": "^26.4.2", "print-coveralls": "^1.2.2", "print-sizes": "^0.2.0", "typescript": "^4.5.0 <4.6.0", "string-hash": "^1.1.3"}}